<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center mb-16">
        <h1 class="text-5xl font-bold text-gray-900 mb-6">Welcome to Chwilio</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover our comprehensive range of services designed to help your business grow and succeed.
        </p>
      </div>
    </section>

    <!-- Services Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Services</h2>
        <p class="text-xl text-gray-600">Everything you need in one place</p>
      </div>

      <!-- Error State -->
      <div v-if="error" class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 class="text-lg font-medium text-red-800 mb-2">Error Loading Services</h3>
          <p class="text-red-600">{{ error.message }}</p>
        </div>
      </div>

      <!-- Services Grid -->
      <div v-else-if="services && services.length > 0"
           class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

        <!-- Each Sub-Category Card -->
        <div v-for="(services, subCategory) in groupedServices"
             :key="subCategory"
             class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">

          <!-- Sub-Category Header -->
          <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 class="text-xl font-bold text-white">{{ subCategory }}</h3>
          </div>

          <!-- Services List -->
          <div class="p-6">
            <ul class="space-y-3">
              <li v-for="service in services" :key="service.id">
                <NuxtLink
                  :to="`/services/${service.link_text}`"
                  class="block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
                >
                  <span class="text-gray-800 group-hover:text-blue-600 font-medium">
                    {{ service.name }}
                  </span>
                  <svg class="inline-block ml-2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 5l7 7-7 7"></path>
                  </svg>
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Debug Info (temporary) -->
      <div v-else class="text-center py-12">
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
          <h3 class="text-lg font-medium text-gray-800 mb-2">Debug Info</h3>
          <div class="text-left text-sm space-y-2">
            <p><strong>Services data:</strong> {{ services }}</p>
            <p><strong>Services length:</strong> {{ services?.length || 0 }}</p>
            <p><strong>Services type:</strong> {{ typeof services }}</p>
            <p><strong>Grouped services:</strong> {{ Object.keys(groupedServices).length }} categories</p>
            <p><strong>Error:</strong> {{ error?.message || 'None' }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// Set page meta
definePageMeta({
  title: 'Home - Chwilio',
  description: 'Welcome to Chwilio - Your comprehensive service provider'
})

// Get Supabase client
const supabase = useSupabaseClient()

// Fetch services data using direct API
const { data: services, error } = await supabase
  .from('services')
  .select('*')

console.log('Services data:', services)
console.log('Error:', error)

// Group services by sub_category
const groupedServices = computed(() => {
  if (!services || !Array.isArray(services)) return {}

  return services.reduce((groups, service) => {
    const subCategory = service.sub_category || 'Other'
    if (!groups[subCategory]) {
      groups[subCategory] = []
    }
    groups[subCategory].push(service)
    return groups
  }, {})
})

// SEO
useHead({
  title: 'Chwilio - Your Service Provider',
  meta: [
    { name: 'description', content: 'Welcome to Chwilio - Browse our comprehensive range of services organized by category' },
    { name: 'keywords', content: 'chwilio, services, business services, home' }
  ]
})
</script>