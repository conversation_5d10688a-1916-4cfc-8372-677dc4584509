<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

      <!-- Loading State -->
      <div v-if="pending" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 class="text-lg font-medium text-red-800 mb-2">Service Not Found</h3>
          <p class="text-red-600 mb-4">{{ error.message }}</p>
          <NuxtLink to="/services"
                    class="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Back to Services
          </NuxtLink>
        </div>
      </div>

      <!-- Service Details -->
      <div v-else-if="service" class="bg-white rounded-xl shadow-lg overflow-hidden">

        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-12 text-center">
          <h1 class="text-4xl font-bold text-white mb-2">{{ service.name }}</h1>
          <p class="text-blue-100 text-lg">{{ service.sub_category }}</p>
        </div>

        <!-- Content -->
        <div class="p-8">
          <!-- Breadcrumb -->
          <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
              <li>
                <NuxtLink to="/" class="hover:text-blue-600 transition-colors">Home</NuxtLink>
              </li>
              <li>/</li>
              <li>
                <NuxtLink to="/services" class="hover:text-blue-600 transition-colors">Services</NuxtLink>
              </li>
              <li>/</li>
              <li class="text-gray-900 font-medium">{{ service.name }}</li>
            </ol>
          </nav>

          <!-- Service Information -->
          <div class="prose max-w-none">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Service</h2>
            <p class="text-gray-600 mb-6">
              Learn more about our {{ service.name }} service in the {{ service.sub_category }} category.
            </p>

            <!-- Add more service details here as needed -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Service Details</h3>
              <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Category</dt>
                  <dd class="text-sm text-gray-900">{{ service.sub_category }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Service ID</dt>
                  <dd class="text-sm text-gray-900">{{ service.id }}</dd>
                </div>
              </dl>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row gap-4">
            <NuxtLink to="/services"
                      class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Back to Services
            </NuxtLink>

            <NuxtLink to="/contact"
                      class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Get Started
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </NuxtLink>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// Get route parameter
const route = useRoute()
const linkText = route.params.id

// Get Supabase client
const supabase = useSupabaseClient()

// Fetch specific service by link_text
const { data: service, pending, error } = await useLazyAsyncData(`service-${linkText}`, async () => {
  try {
    const { data, error } = await supabase
      .from('services')
      .select('id, name, sub_category, link_text, created_at')
      .eq('link_text', linkText)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Service not found')
      }
      console.error('Supabase error:', error)
      throw new Error('Failed to fetch service details')
    }

    return data
  } catch (err) {
    console.error('Error fetching service:', err)
    throw err
  }
})

// SEO and meta
useHead(() => ({
  title: service.value ? `${service.value.name} - Services - Chwilio` : 'Service - Chwilio',
  meta: [
    {
      name: 'description',
      content: service.value
        ? `Learn about our ${service.value.name} service in ${service.value.sub_category}`
        : 'Service details'
    }
  ]
}))

// Handle 404 if service not found
if (import.meta.client && error.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Service Not Found'
  })
}
</script>