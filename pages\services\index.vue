<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Our Services</h1>
        <p class="text-xl text-gray-600">Discover what we can do for you</p>
      </div>

      <!-- Loading State -->
      <div v-if="pending" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 class="text-lg font-medium text-red-800 mb-2">Error Loading Services</h3>
          <p class="text-red-600">{{ error.message }}</p>
        </div>
      </div>

      <!-- Services Grid -->
      <div v-else-if="groupedServices && Object.keys(groupedServices).length > 0" 
           class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        
        <!-- Each Sub-Category Card -->
        <div v-for="(services, subCategory) in groupedServices" 
             :key="subCategory"
             class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
          
          <!-- Sub-Category Header -->
          <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white">{{ subCategory }}</h2>
          </div>
          
          <!-- Services List -->
          <div class="p-6">
            <ul class="space-y-3">
              <li v-for="service in services" :key="service.id">
                <NuxtLink 
                  :to="`/services/${service.link_text}`"
                  class="block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
                >
                  <span class="text-gray-800 group-hover:text-blue-600 font-medium">
                    {{ service.name }}
                  </span>
                  <svg class="inline-block ml-2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" 
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M9 5l7 7-7 7"></path>
                  </svg>
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
          <h3 class="text-lg font-medium text-gray-800 mb-2">No Services Found</h3>
          <p class="text-gray-600">We're working on adding services. Please check back later.</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// Set page meta
definePageMeta({
  title: 'Services - Chwilio',
  description: 'Browse our comprehensive range of services'
})

// Get Supabase client
const supabase = useSupabaseClient()

// Fetch services data securely
const { data: services, pending, error } = await useLazyAsyncData('services', async () => {
  try {
    const { data, error } = await supabase
      .from('services')
      .select('id, name, sub_category, link_text, created_at')
      .order('sub_category', { ascending: true })
      .order('name', { ascending: true })
    
    if (error) {
      console.error('Supabase error:', error)
      throw new Error('Failed to fetch services')
    }
    
    return data || []
  } catch (err) {
    console.error('Error fetching services:', err)
    throw err
  }
})

// Group services by sub_category
const groupedServices = computed(() => {
  if (!services.value) return {}
  
  return services.value.reduce((groups, service) => {
    const subCategory = service.sub_category || 'Other'
    if (!groups[subCategory]) {
      groups[subCategory] = []
    }
    groups[subCategory].push(service)
    return groups
  }, {})
})

// SEO
useHead({
  title: 'Services - Chwilio',
  meta: [
    { name: 'description', content: 'Browse our comprehensive range of services organized by category' },
    { name: 'keywords', content: 'services, chwilio, business services' }
  ]
})
</script>
